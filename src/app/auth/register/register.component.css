@charset "UTF-8";
﻿﻿﻿.register-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.auth-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.error-message {
  margin-bottom: 1.5rem;
}
.error-message ::ng-deep .p-message {
  border-radius: 12px;
  border: none;
  background: #fef2f2;
  color: #dc2626;
  box-shadow: 0 1px 3px rgba(220, 38, 38, 0.1);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: #9ca3af;
  z-index: 10;
  font-size: 1rem;
  pointer-events: none;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #fafafa;
}
.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
.form-input::placeholder {
  color: #9ca3af;
}
.form-input.p-invalid {
  border-color: #dc2626;
  background: #fef2f2;
}

.password-input ::ng-deep .p-password {
  width: 100%;
  display: block;
  position: relative;
}
.password-input ::ng-deep .p-password .p-inputtext {
  width: 100% !important;
  box-sizing: border-box;
  padding: 0.875rem 3rem 0.875rem 2.75rem !important;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #fafafa;
  height: auto;
  min-height: 48px;
}
.password-input ::ng-deep .p-password .p-inputtext:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
.password-input ::ng-deep .p-password .p-inputtext::placeholder {
  color: #9ca3af;
  text-indent: 0;
}
.password-input ::ng-deep .p-password .p-inputtext.p-invalid {
  border-color: #dc2626;
  background: #fef2f2;
}
.password-input ::ng-deep .p-password .p-password-toggle {
  right: 1rem;
  color: #9ca3af;
  z-index: 5;
}
.password-input ::ng-deep .p-password .p-password-toggle:hover {
  color: #6b7280;
}
.password-input ::ng-deep .p-password .p-password-meter {
  margin-top: 0.5rem;
  border-radius: 6px;
  overflow: hidden;
  height: 4px;
}
.password-input ::ng-deep .p-password .p-password-info {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.field-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.terms-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.terms-label {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
  cursor: pointer;
}

.terms-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}
.terms-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

.submit-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 48px;
  margin-top: 0.5rem;
}
.submit-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}
.submit-button:active:not(:disabled) {
  transform: translateY(0);
}
.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner-small {
  width: 20px;
  height: 20px;
}
.spinner-small ::ng-deep .p-progress-spinner-circle {
  stroke: white;
  animation: p-progress-spinner-dash 1.5s ease-in-out infinite;
}

.auth-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.login-prompt {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.login-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.2s ease;
}
.login-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

::ng-deep .p-checkbox.p-invalid .p-checkbox-box {
  border-color: #dc2626;
}

@media (max-width: 480px) {
  .auth-title {
    font-size: 1.75rem;
  }
  .form-input,
  .password-input ::ng-deep .p-inputtext {
    padding: 0.75rem 2.75rem 0.75rem 2.5rem !important;
    font-size: 0.875rem;
  }
  .input-icon {
    left: 0.875rem;
    font-size: 0.875rem;
  }
  .submit-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }
  .terms-wrapper {
    gap: 0.5rem;
  }
  .terms-label {
    font-size: 0.8125rem;
  }
}

/*# sourceMappingURL=register.component.css.map */
